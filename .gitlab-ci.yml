# GitLab CI/CD Pipeline для stop-list-domain (shell executor + docker run)

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  DOCKER_BASE_IMAGE: "gradle:8.5-jdk17"
  KUBE_NAMESPACE: "sl"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - .gradle/daemon

stages:
  - build
  - test
  - publish-snapshot
  - deploy
  - release

.docker_gradle: &docker_gradle
  tags:
    - docker
  before_script:
    - echo "Docker base image $DOCKER_BASE_IMAGE"
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
#  after_script:
#    - chmod -R 777 "$PWD/.gradle" || true
#    - chmod -R 777 "$PWD/build" || true

# ========================
# 1. ЛЮБАЯ ВЕТКА (кроме develop/master): build + test
# ========================

build_and_test:
  <<: *docker_gradle
  stage: test
  script:
    - docker run --rm -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -u `id -u $USER`
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
        ./scripts/setup-gradle-auth.sh
        gradle build --no-daemon
        gradle test --no-daemon
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_TAG == null

# ========================
# 2. DEVELOP: build + test + publish SNAPSHOT (одним шагом)
# ========================

develop_build_test_publish:
  <<: *docker_gradle
  stage: publish-snapshot
  script:
    - docker run --rm -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -u `id -u $USER`
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
        ./scripts/setup-gradle-auth.sh
        gradle build --no-daemon
        gradle test --no-daemon
        gradle publishToNexusSnapshot --no-daemon
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  environment:
    name: develop
    url: https://nexus.sbertroika.tech/repository/maven-snapshots/

# ========================
# 3. MASTER: только release (без отдельного build/test)
# ========================

release:
  <<: *docker_gradle
  stage: release
  before_script:
    - |
      if echo "$CI_COMMIT_MESSAGE" | grep -q "^\[Gradle Release Plugin\]"; then
        echo "Gradle Release Plugin commit detected, skipping job."
        exit 0
      fi
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
    - chown -R $(id -u):$(id -g) .git/ || true
    - chmod -R 755 .git/ || true
    - rm -f .git/index.lock
    - eval $(ssh-agent -s)
    - ssh-add ~/.ssh/id_rsa || echo "SSH key not found, trying alternative locations"
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H git.sbertroika.ru >> ~/.ssh/known_hosts
  script:
    - echo "Выпуск релиза версии"
    - >
      docker run --rm
      -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -v "$SSH_AUTH_SOCK":/tmp/ssh_agent.sock
      -e SSH_AUTH_SOCK=/tmp/ssh_agent.sock
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
        mkdir -p /root/.ssh
        chmod 700 /root/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /root/.ssh/known_hosts
        mkdir -p /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /home/<USER>/.ssh/known_hosts
        export GIT_SSH_COMMAND='ssh -o UserKnownHostsFile=/root/.ssh/known_hosts'
        ./scripts/setup-gradle-auth.sh
        git config --global --add safe.directory /workspace
        git config --global user.name 'GitLab CI'
        git config --global user.email '<EMAIL>'
        git remote set-<NAME_EMAIL>:tkp3/sl/stop-list-domain.git
        git fetch origin master
        git checkout -B master origin/master
        gradle release --no-daemon --info
        chmod -R 777 /workspace
        chmod -R 777 /tmp/.gradle
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $CI_COMMIT_MESSAGE !~ /^\[Gradle Release Plugin\]/
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"
  environment:
    name: production
    url: https://nexus.sbertroika.tech/repository/maven-releases/

# ========================
# STOP-LIST-GATE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
stop_list_gate_build_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_SHORT_SHA"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-gate:$TAG --pull -f stop-list-gate/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-gate:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
  needs: [develop_build_test_publish]

stop_list_gate_helm_kubeval_testing_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - wget https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz
    - tar xvf kubeval-linux-amd64.tar.gz
    - chmod +x ./kubeval
    - ls -la
    - docker run -v $(pwd)/:/app -u `id -u $USER` -i alpine/helm template /app/charts/stop-list-gate/ --output-dir /app/helm_template
    - ls -la
    - ./kubeval ./helm_template/*/*/*
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
  needs: [stop_list_gate_build_develop]

stop_list_gate_deploy_chart_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - SERVICE_NAME="stop-list-gate"
    # Если ветка develop то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "develop" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi
    # Деплой Helm-чарта с установкой образа и его параметрами. Добавлено изменение namespace для ветки develop
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --set namespace=$KUBE_NAMESPACE
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
  needs: [stop_list_gate_helm_kubeval_testing_develop]

# --- TAG ---
stop_list_gate_build_tag:
  stage: build
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_TAG"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-gate:$TAG --pull -f stop-list-gate/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-gate:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-gate/**

stop_list_gate_helm_kubeval_testing_tag:
  stage: test
  tags:
    - docker
  script:
    - wget https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz
    - tar xvf kubeval-linux-amd64.tar.gz
    - chmod +x ./kubeval
    - ls -la
    - docker run -v $(pwd)/:/app -u `id -u $USER` -i alpine/helm template /app/charts/stop-list-gate/ --output-dir /app/helm_template
    - ls -la
    - ./kubeval ./helm_template/*/*/*
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-gate/**
  needs: [stop_list_gate_build_tag]

stop_list_gate_deploy_chart_tag:
  stage: deploy
  tags:
    - docker
  script:
    - SERVICE_NAME="stop-list-gate"
    - TAG="$CI_COMMIT_TAG"
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --set namespace=$KUBE_NAMESPACE
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-gate/**
  needs: [stop_list_gate_helm_kubeval_testing_tag]

# ========================
# STOP-LIST-PROCESSING: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
stop_list_processing_build_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_SHORT_SHA"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-processing:$TAG --pull -f stop-list-processing/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-processing:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-processing/**
  needs: [develop_build_test_publish]

stop_list_processing_helm_kubeval_testing_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - wget https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz
    - tar xvf kubeval-linux-amd64.tar.gz
    - chmod +x ./kubeval
    - ls -la
    - docker run -v $(pwd)/:/app -u `id -u $USER` -i alpine/helm template /app/charts/stop-list-processing/ --output-dir /app/helm_template
    - ls -la
    - ./kubeval ./helm_template/*/*/*
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-processing/**
  needs: [stop_list_processing_build_develop]

stop_list_processing_deploy_chart_develop:
  stage: publish-snapshot
  tags:
    - docker
  script:
    - SERVICE_NAME="stop-list-processing"
    # Если ветка develop то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "develop" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi
    # Деплой Helm-чарта с установкой образа и его параметрами. Добавлено изменение namespace для ветки develop
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --set namespace=$KUBE_NAMESPACE
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-processing/**
  needs: [stop_list_processing_helm_kubeval_testing_develop]

# --- TAG ---
stop_list_processing_build_tag:
  stage: build
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_TAG"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-processing:$TAG --pull -f stop-list-processing/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/stop-list-processing:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-processing/**

stop_list_processing_helm_kubeval_testing_tag:
  stage: test
  tags:
    - docker
  script:
    - wget https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz
    - tar xvf kubeval-linux-amd64.tar.gz
    - chmod +x ./kubeval
    - ls -la
    - docker run -v $(pwd)/:/app -u `id -u $USER` -i alpine/helm template /app/charts/stop-list-processing/ --output-dir /app/helm_template
    - ls -la
    - ./kubeval ./helm_template/*/*/*
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-processing/**
  needs: [stop_list_processing_build_tag]

stop_list_processing_deploy_chart_tag:
  stage: deploy
  tags:
    - docker
  script:
    - SERVICE_NAME="stop-list-processing"
    - TAG="$CI_COMMIT_TAG"
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --set namespace=$KUBE_NAMESPACE
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-processing/**
  needs: [stop_list_processing_helm_kubeval_testing_tag]