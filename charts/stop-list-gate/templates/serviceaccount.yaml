{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "stop-list-gate.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "stop-list-gate.fullname" .) }}
  labels:
    {{- include "stop-list-gate.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
