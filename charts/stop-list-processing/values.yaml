# Default values for stop-list-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: stop-list-processing

replicaCount: 1

image:
  repository: stop-list-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

namespace: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080

resources:
  limits:
    cpu: 1000m
    memory: 2048Mi
  requests:
    cpu: 500m
    memory: 512Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  client:
    logging:
      enable: true
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
  service:
    emv_emission: "emv-emission.emv.svc.cluster.local:5000"
    qr_emission: "qr-emission.qr.svc.cluster.local:5000"
  develop:
    stoplist_lib_enable_log: true
