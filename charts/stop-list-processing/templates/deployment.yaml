apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "stop-list-processing.fullname" . }}
  namespace: {{ .Values.namespace | default (include "stop-list-processing.fullname" .) }}
  labels:
    {{- include "stop-list-processing.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "stop-list-processing.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "stop-list-processing.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "stop-list-processing.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.library.path=/opt/libs", "-Djava.security.egd=file:/dev/./urandom", "-jar", "stop-list-processing.jar"]
          env:
            - name: CLIENT_LOGGING_ENABLE
              value: {{ ternary "true" "false" .Values.env.client.logging.enable | quote }}
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
            - name: EMV_EMISSION_URL
              value: {{ .Values.env.service.emv_emission }}
            - name: QR_EMISSION_URL
              value: {{ .Values.env.service.qr_emission }}
            - name: DEVELOP_STOPLIST_LIB_ENABLE_LOG
              value: {{ ternary "true" "false" .Values.env.develop.stoplist_lib_enable_log | quote }}
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: url
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db
                  key: password
            - name: R2DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: r2dbcUrl
            - name: S3_URL
              valueFrom:
                secretKeyRef:
                  name: sl
                  key: s3Url
            - name: S3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sl
                  key: s3Bucket
            - name: S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: sl
                  key: s3AccessKey
            - name: S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: sl
                  key: s3SecretKey
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
