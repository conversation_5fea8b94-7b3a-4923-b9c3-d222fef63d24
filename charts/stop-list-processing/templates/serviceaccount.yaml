{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "stop-list-processing.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "stop-list-processing.fullname" .) }}
  labels:
    {{- include "stop-list-processing.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
